<template>
	<div class="">
		<div class="chart-wrapper">
			<LineChart
				:chart-data="chartData1"
				:color-list="colors"
				yAxisName="次数"
				unit="次"
			/>
		</div>
		<div class="chart-wrapper">
			<LineChart
				:chart-data="chartData"
				:color-list="colorList1"
				:show-split-area="false"
				:smooth="true"
				yAxisName="个/片"
				unit="个/片"
			/>
		</div>
	</div>
</template>

<script setup>
import LineChart from '@c/charts/LineChart.vue'

import { genChartMockData } from '@/utils/public.js'

const colors = ['#1460c1', '#a0deff', '#ffa82c', '#ff8430', '#2fc5e9']

const xData = ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
const chartData1 = {
	xData,
	seriesData: [
		{
			name: 'Pop',
			data: genChartMockData(xData)
		},
		{
			name: 'Imap',
			data: genChartMockData(xData)
		},
		{
			name: 'Web',
			data: genChartMockData(xData)
		},
		{
			name: 'Smtp',
			data: genChartMockData(xData)
		},
		{
			name: 'Lunkr',
			data: genChartMockData(xData)
		}
	]
}

const xMonthData = [
	'1月',
	'2月',
	'3月',
	'4月',
	'5月',
	'6月',
	'7月',
	'8月',
	'9月',
	'10月',
	'11月',
	'12月'
]

const colorList1 = ['#4ce0fb', '#3a8bf0', '#6f50f3', '#60f478']
const chartData = {
	xData: xMonthData,
	seriesData: [
		{
			name: '结石',
			type: 'bar',
			barWidth: 10,
			stack: '结石',
			stackOrder: 'seriesDesc',
			data: genChartMockData(xMonthData)
		},
		{
			name: '铂金',
			type: 'bar',
			barWidth: 10,
			stack: '结石',
			stackOrder: 'seriesDesc',
			data: genChartMockData(xMonthData)
		},
		{
			name: '气泡',
			type: 'bar',
			barWidth: 10,
			stack: '结石',
			stackOrder: 'seriesDesc',
			data: genChartMockData(xMonthData)
		},
		{
			name: '修复数',
			smooth: false,
			data: genChartMockData(xMonthData)
		}
	]
}
</script>

<style scoped lang="scss">
.chart-wrapper {
	width: 3000px;
	height: 1200px;
}
</style>
