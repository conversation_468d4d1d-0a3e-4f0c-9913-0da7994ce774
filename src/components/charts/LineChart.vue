<template>
	<div ref="chartRef" class="line-chart"></div>
</template>

<script setup>
import * as echarts from 'echarts'

import { keepNumBitFilter } from '@/utils/filters.js'
import { set16ToRgb } from '@/utils/public.js'

const props = defineProps({
	chartData: {
		required: true,
		type: Object,
		default: () => {
			return {
				xData: [],
				seriesData: []
			}
		}
	},
	showSplitArea: {
		type: Boolean,
		default: () => true
	},
	// 折线图区域颜色
	areaStyle: {
		type: Boolean,
		default: () => false
	},
	// x轴名称
	xAxisName: {
		type: String,
		default: () => ''
	},
	// y轴名称,
	yAxisName: {
		type: [String, Array],
		default: () => ''
	},
	unit: {
		type: String,
		default: () => 'MWh'
	},
	colorList: {
		type: Array,
		default: () => ['#28F2B2', '#28F0F2', '#28ADF2']
	},
	// 是否是多y轴
	multiYAxis: {
		type: <PERSON>olean,
		default: () => false
	}
})

const emit = defineEmits(['updateOp'])

const chartRef = ref(null)
let myChart = null

// 根据 stackOrder 配置对 series 进行排序
const sortSeriesByStackOrder = seriesArr => {
	// 分离堆叠和非堆叠的 series
	const stackedSeries = []
	const nonStackedSeries = []
	const stackGroups = {}

	seriesArr.forEach(series => {
		if (series.stack) {
			if (!stackGroups[series.stack]) {
				stackGroups[series.stack] = []
			}
			stackGroups[series.stack].push(series)
		} else {
			nonStackedSeries.push(series)
		}
	})
	console.log(stackGroups, '@stackGroups')
	// 对每个堆叠组进行排序
	Object.keys(stackGroups).forEach(stackName => {
		const group = stackGroups[stackName]
		console.log(group, '@group')
		const result = []
		stackedSeries.push(...group.reverse())
	})

	// 返回排序后的 series 数组
	return [...stackedSeries, ...nonStackedSeries]
}

const chartOp = reactive({
	grid: {
		top: 40,
		left: 20,
		right: 30,
		bottom: 20,
		containLabel: true
	},
	legend: {
		show: true,
		icon: 'rect',
		itemWidth: 12,
		itemHeight: 1,
		textStyle: {
			color: '#C0E5F1',
			fontWeight: 'normal'
		}
	},
	tooltip: {
		trigger: 'axis',
		axisPointer: {
			type: 'line'
		},
		confine: true,
		backgroundColor: 'rgba(0, 0, 0, 0.8)',
		borderColor: '#00E4FF',
		borderRadius: 8,
		textStyle: {
			zIndex: 100,
			fontSize: 14,
			color: '#fff'
		},
		formatter: params => {
			const resArr = reactive([params[0].name])
			for (let i = 0; i < params.length; i++) {
				const { color, seriesName, data } = params[i]
				if (seriesName) {
					resArr.push(
						`<span style="color: ${
							color?.type ? props.colorList[resArr.length - 1] : color
						}">${seriesName}: ${keepNumBitFilter(data[1] ?? data)} ${
							props.unit
						}</span>`
					)
				}
			}
			return resArr.join('<br />')
		}
	},
	xAxis: {
		axisTick: {
			show: false
		},
		boundaryGap: true,
		axisLabel: {
			color: '#C0E5F1',
			fontSize: 14
		},
		axisLine: {
			lineStyle: {
				color: 'rgba(235,250,255, 0.5)',
				width: 1
			}
		},
		data: []
	},
	yAxis: [
		{
			name: '',
			nameTextStyle: {
				align: 'left',
				color: '#C0E5F1',
				padding: [0, 0, 0, -20]
			},
			type: 'value',
			axisTick: {
				show: false
			},
			splitLine: {
				show: false,
				lineStyle: {
					color: 'rgba(235,250,255, 0.1)'
				}
			},
			splitArea: {
				show: props.showSplitArea,
				areaStyle: {
					color: ['rgba(235,250,255, 0)', 'rgba(235,250,255, 0.1)']
				}
			},
			axisLabel: {
				color: '#C0E5F1',
				fontSize: 14
			},
			axisLine: {
				show: true,
				lineStyle: {
					color: 'rgba(235,250,255, 0.5)',
					width: 1
				}
			},
			data: []
		}
	],
	series: []
})

const drawChart = () => {
	if (!chartRef.value) return
	if (myChart) {
		myChart.dispose()
	}
	myChart = echarts.init(chartRef.value)

	const {
		colorList,
		xAxisName,
		yAxisName,
		areaStyle,
		chartData: { xData, seriesData }
	} = props
	chartOp.color = colorList
	chartOp.xAxis.data = xData
	chartOp.xAxis.name = xAxisName || ''
	if (Array.isArray(yAxisName)) {
		chartOp.yAxis.forEach((item, index) => {
			item.name = yAxisName[index]
		})
	} else {
		chartOp.yAxis[0].name = yAxisName
	}

	// 处理 series 数据
	const seriesArr = reactive([])
	let isStackSort = false
	for (const key in seriesData) {
		if (seriesData[key]) {
			const obj = {
				smooth: true,
				symbol: 'none',
				type: 'line',
				lineStyle: {
					width: 2
				},
				itemStyle: {
					color: colorList[key]
				}
			}
			if (seriesData[key].type === 'bar') {
				obj.itemStyle = {
					...(seriesData[key]?.itemStyle || {}),
					color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
						{
							offset: 0,
							color: set16ToRgb(colorList[key], 1)
						},
						{
							offset: 1,
							color: set16ToRgb(colorList[key], 0.1)
						}
					])
				}
				if (seriesData[key].stack) {
					isStackSort = true
				}
			}
			if (areaStyle && seriesData[key].type === 'line') {
				obj.areaStyle = {
					color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
						{
							offset: 0,
							color: set16ToRgb(colorList[key], 0.6)
						},
						{
							offset: 1,
							color: set16ToRgb(colorList[key], 0.2)
						}
					])
				}
			}

			// 移除 series 级别的 stackOrder，因为它应该通过排序来实现
			const seriesItem = { ...seriesData[key] }
			delete seriesItem.stackOrder

			seriesArr.push({
				...obj,
				...seriesItem
			})
		}
	}
	chartOp.series = isStackSort ? sortSeriesByStackOrder(seriesArr) : seriesArr

	emit('updateOp', chartOp)
	myChart.clear()
	myChart.setOption(chartOp)
}

watch(
	() => props.chartData,
	val => {
		nextTick(() => {
			drawChart()
		})
	},
	{
		immediate: true,
		deep: true
	}
)

const resizeChart = () => {
	if (myChart) {
		myChart.resize()
	}
}

onMounted(() => {
	window.addEventListener('resize', resizeChart)
})

onBeforeUnmount(() => {
	if (myChart) myChart.dispose()
	window.removeEventListener('resize', resizeChart)
})
</script>

<style lang="scss" scoped>
.line-chart {
	width: 100%;
	height: 100%;
}
</style>
