<!-- 仪表盘图表 -->
<template>
	<div ref="chartRef" class="dashboard-chart"></div>
</template>

<script setup>
import * as echarts from 'echarts'

const props = defineProps({
	value: {
		type: Number,
		default: 77.5
	}
})

const chartRef = ref(null)
let myChart = null

const option = {
	series: [
		{
			type: 'pie',
			animation: false,
			emphasis: {
				disabled: true
			},
			tooltip: {
				show: false
			},
			radius: ['0%', '10%'],
			center: ['50%', '80%'],
			label: {
				show: false
			},
			labelLine: {
				show: false
			},
			data: [
				{
					value: 100,
					itemStyle: {
						color: '#FFF',
						shadowColor: '#1E91F8',
						shadowBlur: 30
					}
				}
			]
		},
		{
			type: 'pie',
			animation: false,
			emphasis: {
				disabled: true
			},
			tooltip: {
				show: false
			},
			radius: ['0%', '8%'],
			center: ['50%', '80%'],
			label: {
				show: false
			},
			labelLine: {
				show: false
			},
			data: [
				{
					value: 100,
					itemStyle: {
						color: '#1E91F8'
					}
				}
			]
		},
		{
			type: 'gauge',
			radius: '40%',
			center: ['50%', '80%'],
			min: 0,
			max: 100,
			startAngle: 180,
			endAngle: 0,
			axisLine: {
				show: true,
				lineStyle: {
					width: '100%',
					color: [[1, 'rgba(21, 38, 66, 0.9)']]
				}
			},
			progress: {
				show: true,
				width: 3,
				itemStyle: {
					color: '#303E57'
				}
			},
			axisTick: {
				show: false
			},
			splitLine: {
				show: false
			},
			axisLabel: {
				show: false
			},
			pointer: {
				show: false
			},
			detail: {
				show: false
			},
			data: [{ value: 100 }]
		},
		{
			type: 'gauge',
			startAngle: 180,
			endAngle: 0,
			radius: '100%',
			center: ['50%', '80%'],
			min: 0,
			max: 100,
			splitNumber: 15,
			z: 10,
			axisLine: {
				show: true,
				lineStyle: {
					width: 15,
					color: [[1, '#202F49']] // 颜色
				}
			},
			progress: {
				show: true,
				width: 15,
				itemStyle: {
					color: new echarts.graphic.LinearGradient(
						0,
						0,
						1,
						0,
						[
							{
								offset: 0,
								color: '#22ECD1'
							},
							{
								offset: 1,
								color: '#2FA3F9'
							}
						],
						false
					)
				}
			},
			axisTick: {
				show: true,
				length: 15,
				distance: 20,
				lineStyle: {
					color: '#434F65',
					width: 1
				}
			},
			splitLine: {
				show: true,
				length: -8,
				distance: 28,
				lineStyle: {
					color: '#B4BCC9',
					width: 1
				}
			},
			pointer: {
				length: '70%',
				width: 15,
				offsetCenter: [0, -8],
				itemStyle: {
					color: {
						type: 'linear',
						x: 0,
						y: 0,
						x2: 0,
						y2: 1,
						colorStops: [
							{ offset: 0, color: '#1E91F8' },
							{ offset: 1, color: 'rgba(30,145,248, 0)' }
						]
					}
				}
			},
			axisLabel: {
				show: true,
				distance: -58,
				color: '#B2CADD',
				fontSize: 20,
				formatter(value) {
					return value % 20 === 0 ? value : ''
				}
			},
			detail: {
				formatter: value => `{val|${value}}   {text|年综合效率}`,
				rich: {
					val: {
						fontSize: 28,
						fontWeight: 'bold',
						color: '#1E91F8'
					},
					text: {
						fontSize: 28,
						color: '#B2CADD'
					}
				},
				color: '#fff',
				fontSize: 28,
				fontWeight: 'bold',
				offsetCenter: [0, '30%'], // 把数值文字下移一些
				valueAnimation: true
			},
			// markLine: {
			// 	silent: true,
			// 	symbol: 'none',
			// 	lineStyle: {
			// 		color: '#434F65',
			// 		width: 2,
			// 		type: 'solid'
			// 	},
			// 	data: [
			// 		{
			// 			yAxis: 0,
			// 			label: {
			// 				show: false
			// 			}
			// 		}
			// 	]
			// },
			data: [{ value: props.value }]
		}
	]
}

const resizeChart = () => {
	if (myChart) {
		myChart.resize()
	}
}

const getHorizontalLineGraphic = () => {
	const chartDom = chartRef.value
	const width = chartDom?.offsetWidth || 400
	const height = chartDom?.offsetHeight || 300

	// 仪表盘中心和半径
	const centerX = width * 0.5
	const centerY = height * 0.8
	const radius = Math.min(width, height) * 0.52 // 超出gauge的宽度

	// 横线起止点
	const x1 = centerX - radius
	const x2 = centerX + radius
	const y = centerY

	return {
		type: 'line',
		shape: {
			x1,
			y1: y,
			x2,
			y2: y
		},
		style: {
			stroke: '#364349',
			lineWidth: 2,
			shadowBlur: 0
		},
		z: 1,
		silent: true
	}
}

const getPointerCircleGraphic = value => {
	const chartDom = chartRef.value
	const width = chartDom?.offsetWidth || 400
	const height = chartDom?.offsetHeight || 300

	// 仪表盘中心和半径
	const centerX = width * 0.5
	const centerY = height * 0.8
	const radius = Math.min(width, height) * 0.48
	const startAngle = 180
	const endAngle = 0
	const min = 0
	const max = 100

	const angle =
		((startAngle - ((value - min) / (max - min)) * (startAngle - endAngle)) *
			Math.PI) /
		180

	const cx = centerX + radius * Math.cos(angle)
	const cy = centerY - radius * Math.sin(angle)

	return {
		type: 'circle',
		shape: {
			cx,
			cy,
			r: 6
		},
		style: {
			fill: '#1E91F8',
			stroke: '#fff',
			lineWidth: 4,
			shadowColor: '#1E91F8',
			shadowBlur: 10
		},
		z: 100
	}
}

const setChartOption = value => {
	const optionWithGraphic = {
		...option,
		graphic: {
			elements: [getHorizontalLineGraphic(), getPointerCircleGraphic(value)]
		}
	}
	myChart.setOption(optionWithGraphic, true)
}

watch(
	() => props.value,
	newVal => {
		setChartOption(newVal)
	}
)

onMounted(() => {
	myChart = echarts.init(chartRef.value)
	setChartOption(props.value)
	window.addEventListener('resize', resizeChart)
})

onBeforeUnmount(() => {
	if (myChart) {
		myChart.dispose()
	}
	window.removeEventListener('resize', resizeChart)
})
</script>

<style scoped lang="scss">
.dashboard-chart {
	width: 100%;
	height: 100%;
}
</style>
