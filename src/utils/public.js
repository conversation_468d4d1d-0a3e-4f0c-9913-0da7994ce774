import axios from 'axios'

import { keepNumBitFilter } from '@/utils/filters.js'

// 导出excel
export function exportMethod(data) {
	axios({
		// 导出方法调用时 会传入对象作为参数 对象中会标识 请求方法 url(绝对路径) 需要导出的数据id数组等
		method: data.method,
		baseURL: '',
		url: data.url,
		data: data.par,
		// 此时后台返回的数据会被强制转为blob类型 若有error需后台直接throw
		responseType: 'blob'
	})
		.then(res => {
			const link = document.createElement('a')
			// 创建一个blob对象 并规定为.xlsx类型
			const blob = new Blob([res.data], {
				type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
			})
			// 使新创建的a标签不可见
			link.style.display = 'none'
			// blob对象在内存中的url
			link.href = URL.createObjectURL(blob)
			// link.download = res.headers['content-disposition'] //下载后文件名
			// 设置a标签的download属性值 其值规定点击a标签下载文件时 下载文件的名称
			// ps:只有 Firefox 和 Chrome 支持 download 属性
			link.download = data.fileName
			document.body.appendChild(link)
			// 浏览器会自动执行对于文件流的下载
			link.click()
			document.body.removeChild(link)
		})
		.catch(error => {
			Message({
				message: error.message,
				type: 'error',
				title: '错误',
				desc: '网络连接错误'
			})
		})
}

// 日期格式化
export function dateFormat(f, d) {
	const date = d ? new Date(d) : new Date()
	let fmt = f || 'YYYY-MM-DD'
	let ret
	const weekArr = ['日', '一', '二', '三', '四', '五', '六']
	const opt = {
		'Y+': date.getFullYear().toString(), // 年
		'M+': (date.getMonth() + 1).toString(), // 月
		'D+': date.getDate().toString(), // 日
		'H+': date.getHours().toString(), // 时
		'm+': date.getMinutes().toString(), // 分
		's+': date.getSeconds().toString(), // 秒
		'W+': date.getDay() // 星期    // 有其他格式化字符需求可以继续添加，必须转化成字符串
	}
	for (const k in opt) {
		if (opt.hasOwnProperty(k)) {
			ret = new RegExp(`(${k})`).exec(fmt)
			if (ret) {
				const temp = k === 'W+' ? weekArr[opt[k]] : opt[k]
				fmt = fmt.replace(
					ret[1],
					ret[1].length === 1 ? temp : opt[k].padStart(ret[1].length, '0')
				)
			}
		}
	}
	return fmt
}

export const getCookie = name => {
	if (document.cookie.length > 0) {
		let c_start = document.cookie.indexOf(`${name}=`)
		if (c_start !== -1) {
			c_start = c_start + name.length + 1
			let c_end = document.cookie.indexOf(';', c_start)
			if (c_end === -1) {
				c_end = document.cookie.length
			}
			return unescape(document.cookie.substring(c_start, c_end))
		}
	}
	return ''
}

export const setCookie = (c_name, value, expiredays) => {
	const exdate = new Date()
	exdate.setDate(exdate.getDate() + expiredays)
	document.cookie = `${c_name}=${escape(value)}${
		expiredays == null ? '' : `;expires=${exdate.toGMTString()}`
	}`
}

export const clearCookie = name => {
	setCookie(name, '', -1)
}

// 将传入的px转为rem(行内样式)
export function pxToRem(styleObj) {
	// 默认16px=1rem
	for (const key in styleObj) {
		if (typeof styleObj[key] === 'string') {
			if (styleObj[key].includes('px')) {
				styleObj[key] = `${parseFloat(styleObj[key].replace('px', '')) / 16}rem`
			}
		} else if (typeof styleObj[key] === 'number') {
			styleObj[key] = `${parseFloat(styleObj[key]) / 16}rem`
		}
	}
	return styleObj
}

// 16进制转换为RGBA
export function set16ToRgb(str, opcity = 1) {
	/* eslint-disable-next-line no-useless-escape  */
	const reg = /^#([0-9A-Fa-f]{3}|[0-9A-Fa-f]{6})$/
	if (!reg.test(str)) {
		return
	}
	/* eslint-disable-next-line no-useless-escape  */
	let newStr = str.toLowerCase().replace(/\#/g, '')
	const len = newStr.length
	if (len === 3) {
		let t = ''
		for (let i = 0; i < len; i++) {
			t += newStr.slice(i, i + 1).concat(newStr.slice(i, i + 1))
		}
		newStr = t
	}
	const arr = [] // 将字符串分隔，两个两个的分隔
	for (let i = 0; i < 6; i += 2) {
		const s = newStr.slice(i, i + 2)
		arr.push(parseInt(`0x${s}`, 16))
	}
	return `rgba(${arr.join(',')}, ${opcity})`
}

// 数据保留位数处理
export function formatNumber(val, fixedNum) {
	fixedNum = fixedNum ?? 1
	let realVal = ''
	if (val && !Number.isNaN(+val)) {
		if (+val === parseInt(val, 10)) {
			realVal = val
		} else {
			realVal = parseFloat(val).toFixed(fixedNum)
		}
	} else if (val == null || val === undefined) {
		realVal = '-'
	} else {
		realVal = val
	}
	return realVal
}

// 获取上月月第一天和最后一天
export function getStartOfLastMonth() {
	const now = new Date()
	now.setMonth(now.getMonth() - 1) // 减去1个月
	// 本月的开始时间
	now.setDate(1) // 设置日期为该月的第1天
	now.setHours(0, 0, 0, 0) // 设置时间为0时0分0秒0毫秒
	// 本月的结束时间
	const end = new Date()
	end.setDate(0) // 设置本月的第0天，即上月最后一天
	end.setHours(23, 59, 59, 999) // 设置时间为0时0分0秒0毫秒
	return {
		startTime: dateFormat('YYYY-MM-DD HH:mm:ss', now),
		endTime: dateFormat('YYYY-MM-DD HH:mm:ss', end)
	}
}

// 获取本月第一天和最后一天
export function getMonthFistAndLastDay() {
	const now = new Date() // 当前日期
	const nowMonth = now.getMonth() // 当前月
	const nowYear = now.getFullYear() // 当前年
	// 本月的开始时间
	const monthStartDate = new Date(nowYear, nowMonth, 1)
	// 本月的结束时间
	const monthEndDate = new Date(nowYear, nowMonth + 1, 0)
	return {
		startTime: `${dateFormat('YYYY-MM-DD', monthStartDate)} 00:00:00`,
		endTime: `${dateFormat('YYYY-MM-DD', monthEndDate)} 23:59:59`
	}
}

// 根据当前分辨率，动态计算echarts的 fontSize、barWidth
export function getFontsize(val, initWidth = 1920) {
	const nowClientWidth = document.documentElement.clientWidth
	return val * (nowClientWidth / initWidth)
}

// 获取当月前N个月，不包括当月,跨年
export function getBeforeMonth(f, n) {
	const date = new Date()
	let year = date.getFullYear()
	let month = date.getMonth() + 1
	const day = date.getDate()
	if (month < n) {
		year -= 1
		month += 12 - n
	} else {
		month -= n
	}
	return dateFormat(f, `${year}-${month}-${day}`)
}

// 获取指定月份的天数(默认本月)
export function getMonthDays(date) {
	date = date ? new Date(date) : new Date()
	const y = date.getFullYear()
	const m = date.getMonth() + 1
	return new Date(y, m, 0).getDate()
}

/**
 * 获取本年近n个月(默认到上月月底, 默认6个月)
 * @param {*} n 月份
 * @param {*} flag true 包含当月; false 不包含当月
 * @returns
 */
export function getDaterange(n = 6, flag = false) {
	const now = new Date()
	const y = now.getFullYear()
	const m = now.getMonth()
	let btm = 0
	if (m >= n) {
		btm = flag ? m - n + 1 : m - n
	}
	const bt = new Date(y, btm, 1)
	const et = new Date(y, flag ? m + 1 : m, 0)
	return {
		startTime: dateFormat('YYYY-MM-DD', bt),
		endTime: dateFormat('YYYY-MM-DD', et)
	}
}

/**
 * 数据除以倍数
 * @param {*} val 			目标数据
 * @param {*} multiple 	倍数
 * @param {*} fixedNum 	保留的小数位数
 * @returns	(50000, 10000) -> 5
 */
export function divideByMultiple(val, multiple, fixedNum = 2) {
	if (!Number.isNaN(parseFloat(val))) {
		return keepNumBitFilter(val / multiple, fixedNum)
	}
	return '-'
}

/**
 * echarts数据除以倍数
 * @param {*} dataList	[['01', '11'], ['02', '22']...]
 * @param {*} multiple 	倍数
 * @param {*} fixedNum 	保留的小数位数
 */
export function chartDataDivideByMultiple(dataList, multiple, fixedNum) {
	if (dataList?.length) {
		dataList.forEach(item => {
			item[1] = divideByMultiple(item[1], multiple, fixedNum)
		})
	}
}

// px转换vw
export function px2vw(px, width = 1920) {
	return `${(px / width) * 100}vw`
}
// px转换vh
export function px2vh(px, height = 1080) {
	return `${(px / height) * 100}vh`
}

// 获取响应式尺寸
export function getResponsiveSize(basePx) {
	const baseWidth = 9600
	const currentWidth = document.documentElement.clientWidth
	return basePx * (currentWidth / baseWidth)
}

// 生成echarts图X轴数据
export function utilsGetXData(type) {
	type = type || 'day'
	const arr = []
	if (type === 'day') {
		for (let i = 0; i < 24; i++) {
			arr.push(`${i}:00`.padStart(5, '0'))
		}
	} else {
		const len = type === 'year' ? 12 : getMonthDays()
		for (let i = 1; i <= len; i++) {
			arr.push(`${`${i}`}`.padStart(2, '0'))
		}
	}
	return arr
}

// 生成echarts图二维数组
export function genChartMockData(arr, type = '') {
	arr = arr || utilsGetXData()
	const res = []
	for (let i = 0; i < arr.length; i++) {
		let mockValue = Math.floor(Math.random() * 90 + 10)
		let value = +arr[i]
		if (type === 'day' && arr[i].indexOf(':') > -1) {
			value = +arr[i].split(':')[0]
		}
		if (type && !Number.isNaN(value)) {
			const now = new Date()
			const currentMonth = now.getMonth() + 1
			const currentDate = now.getDate()
			const currentHour = now.getHours()
			if (
				(type === 'year' && value > currentMonth) ||
				(type === 'month' && value > currentDate) ||
				(type === 'day' && value > currentHour)
			) {
				mockValue = null
			}
		}
		res.push([arr[i], mockValue])
	}
	return res
}
