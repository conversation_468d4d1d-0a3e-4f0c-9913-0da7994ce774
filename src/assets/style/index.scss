@use './reset.css';
@use './element-plus.scss';

#app {
	height: 100%;
	background-color: #011022;
}

// 文字超出...
.text-overflow {
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
}

// 标题文字渐变色
.title-text-gradient {
	background-image: linear-gradient(to bottom, #ffffff 40%, #67cfff 80%);
	-webkit-text-fill-color: transparent;
	-webkit-background-clip: text;
}

::-webkit-scrollbar {
	width: 0;
	height: 10px;
}

::-webkit-scrollbar-track {
	box-shadow: 0px 1px 2px rgba($color: #0089f0, $alpha: 0.1) inset;
	border-radius: 5px;
	background-color: rgba($color: #0089f0, $alpha: 0.1);
}

::-webkit-scrollbar-thumb {
	box-shadow: 0px 1px 2px rgba(0, 137, 240, 0.6) inset;
	border-radius: 8px;
	background-color: rgba(0, 137, 240, 0.6);
}
